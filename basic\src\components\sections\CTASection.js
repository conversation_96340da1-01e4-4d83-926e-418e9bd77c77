'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/Button'
import { ArrowRight, MessageCircle, Calendar } from 'lucide-react'

export default function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
      
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl">
            Ready to Transform Your Business?
          </h2>
          <p className="mt-6 text-xl text-blue-100 max-w-3xl mx-auto">
            Join thousands of satisfied clients who have already transformed their businesses with our innovative solutions. 
            Let's discuss how we can help you achieve your goals.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-12 flex flex-col sm:flex-row gap-6 justify-center items-center"
        >
          <Button 
            size="lg" 
            variant="secondary"
            className="group bg-white text-blue-600 hover:bg-gray-100"
          >
            <MessageCircle className="mr-2 h-5 w-5" />
            Start Free Consultation
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
          
          <Button 
            size="lg" 
            variant="outline"
            className="group border-white text-white hover:bg-white hover:text-blue-600"
          >
            <Calendar className="mr-2 h-5 w-5" />
            Schedule a Demo
          </Button>
        </motion.div>

        {/* Contact Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
        >
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
            <div className="text-2xl font-bold text-white mb-2">24/7</div>
            <div className="text-blue-100">Support Available</div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
            <div className="text-2xl font-bold text-white mb-2">48hrs</div>
            <div className="text-blue-100">Response Time</div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
            <div className="text-2xl font-bold text-white mb-2">100%</div>
            <div className="text-blue-100">Satisfaction Guarantee</div>
          </div>
        </motion.div>

        {/* Additional CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-12 text-center"
        >
          <p className="text-blue-100 mb-4">
            Have questions? We're here to help!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-blue-100">
            <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
              📧 <EMAIL>
            </a>
            <span className="hidden sm:inline">•</span>
            <a href="tel:+15551234567" className="hover:text-white transition-colors">
              📞 +****************
            </a>
          </div>
        </motion.div>
      </div>

      {/* Floating Elements */}
      <motion.div
        animate={{ 
          y: [-20, 20, -20],
          rotate: [0, 5, 0, -5, 0]
        }}
        transition={{ 
          duration: 6, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
        className="absolute top-20 left-10 w-16 h-16 bg-yellow-400/20 rounded-full blur-xl"
      />
      
      <motion.div
        animate={{ 
          y: [20, -20, 20],
          rotate: [0, -5, 0, 5, 0]
        }}
        transition={{ 
          duration: 8, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
        className="absolute bottom-20 right-10 w-20 h-20 bg-pink-400/20 rounded-full blur-xl"
      />
    </section>
  )
}
