'use client'

import { motion } from 'framer-motion'
import { Star, Quote } from 'lucide-react'

const testimonials = [
  {
    content: "Working with Modern Business transformed our digital presence completely. Their team delivered beyond our expectations with exceptional quality and professionalism.",
    author: "<PERSON>",
    role: "CEO, TechStart Inc.",
    avatar: "SJ",
    rating: 5,
    company: "TechStart Inc."
  },
  {
    content: "The level of expertise and attention to detail is outstanding. They helped us scale our platform to handle millions of users seamlessly.",
    author: "<PERSON>",
    role: "CTO, GrowthCorp",
    avatar: "MC",
    rating: 5,
    company: "GrowthCorp"
  },
  {
    content: "Incredible results! Our website performance improved by 300% and user engagement skyrocketed. Highly recommend their services.",
    author: "<PERSON>",
    role: "Marketing Director, InnovateLab",
    avatar: "ER",
    rating: 5,
    company: "InnovateLab"
  },
  {
    content: "Professional, reliable, and innovative. They delivered our mobile app on time and within budget. The ongoing support has been excellent.",
    author: "<PERSON>",
    role: "Founder, StartupXYZ",
    avatar: "DT",
    rating: 5,
    company: "StartupXYZ"
  },
  {
    content: "Their cloud solutions helped us reduce costs by 40% while improving our system reliability. The migration was seamless and well-planned.",
    author: "<PERSON>",
    role: "IT Director, Enterprise Solutions",
    avatar: "LW",
    rating: 5,
    company: "Enterprise Solutions"
  },
  {
    content: "Outstanding customer service and technical expertise. They've been our trusted technology partner for over 3 years now.",
    author: "Robert Martinez",
    role: "Operations Manager, RetailPlus",
    avatar: "RM",
    rating: 5,
    company: "RetailPlus"
  }
]

export default function TestimonialsSection() {
  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            What Our Clients Say
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            Don't just take our word for it - hear from our satisfied clients
          </p>
        </motion.div>

        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="relative bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 hover:shadow-lg transition-shadow"
            >
              {/* Quote Icon */}
              <div className="absolute top-4 right-4">
                <Quote className="h-6 w-6 text-blue-500 opacity-50" />
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-gray-700 dark:text-gray-300 mb-6">
                "{testimonial.content}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold text-sm">
                    {testimonial.avatar}
                  </div>
                </div>
                <div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {testimonial.author}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {testimonial.role}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400">
                    {testimonial.company}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Trusted by Industry Leaders
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
              {/* Placeholder for company logos */}
              {['TechStart', 'GrowthCorp', 'InnovateLab', 'StartupXYZ'].map((company, index) => (
                <div key={index} className="text-center">
                  <div className="h-12 w-24 mx-auto bg-gray-300 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {company}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
