"use client";

import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import {
  Code,
  Smartphone,
  Cloud,
  Headphones,
  Database,
  Shield,
  BarChart3,
  Zap,
  ArrowRight,
  Check,
} from "lucide-react";

const services = [
  {
    title: "Web Development",
    description:
      "Custom web applications built with modern technologies and best practices for optimal performance and user experience.",
    icon: Code,
    features: [
      "React & Next.js Development",
      "Full-stack Solutions",
      "RESTful API Development",
      "Database Design & Integration",
      "Responsive Design",
      "Performance Optimization",
    ],
    pricing: "Starting at $5,000",
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Mobile App Development",
    description:
      "Native and cross-platform mobile applications for iOS and Android with seamless user experiences.",
    icon: Smartphone,
    features: [
      "React Native Development",
      "iOS Native Development",
      "Android Native Development",
      "App Store Optimization",
      "Push Notifications",
      "Offline Functionality",
    ],
    pricing: "Starting at $8,000",
    color: "from-purple-500 to-pink-500",
  },
  {
    title: "Cloud Solutions",
    description:
      "Scalable cloud infrastructure and deployment solutions for modern applications.",
    icon: Cloud,
    features: [
      "AWS & Azure Deployment",
      "DevOps & CI/CD",
      "Microservices Architecture",
      "Auto-scaling Solutions",
      "Load Balancing",
      "Monitoring & Analytics",
    ],
    pricing: "Starting at $3,000",
    color: "from-green-500 to-teal-500",
  },
  {
    title: "Database Solutions",
    description:
      "Robust database design, optimization, and management for your applications.",
    icon: Database,
    features: [
      "Database Design",
      "Performance Optimization",
      "Data Migration",
      "Backup & Recovery",
      "Security Implementation",
      "Scalability Planning",
    ],
    pricing: "Starting at $2,500",
    color: "from-orange-500 to-red-500",
  },
  {
    title: "Security & Compliance",
    description:
      "Comprehensive security audits and compliance solutions to protect your business.",
    icon: Shield,
    features: [
      "Security Audits",
      "Penetration Testing",
      "GDPR Compliance",
      "Data Encryption",
      "Access Control",
      "Security Training",
    ],
    pricing: "Starting at $4,000",
    color: "from-indigo-500 to-purple-500",
  },
  {
    title: "Analytics & Insights",
    description:
      "Data analytics and business intelligence solutions to drive informed decisions.",
    icon: BarChart3,
    features: [
      "Custom Dashboards",
      "Real-time Analytics",
      "Data Visualization",
      "Performance Metrics",
      "User Behavior Analysis",
      "Reporting Automation",
    ],
    pricing: "Starting at $3,500",
    color: "from-pink-500 to-rose-500",
  },
];

const processSteps = [
  {
    step: "01",
    title: "Discovery & Planning",
    description:
      "We start by understanding your business goals, requirements, and challenges to create a comprehensive project plan.",
  },
  {
    step: "02",
    title: "Design & Prototyping",
    description:
      "Our design team creates wireframes and prototypes to visualize the solution before development begins.",
  },
  {
    step: "03",
    title: "Development & Testing",
    description:
      "We build your solution using agile methodologies with continuous testing and quality assurance.",
  },
  {
    step: "04",
    title: "Deployment & Support",
    description:
      "We deploy your solution and provide ongoing support, maintenance, and optimization services.",
  },
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
              Our Services
            </h1>
            <p className="mt-6 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comprehensive technology solutions designed to help your business
              grow, scale, and succeed in the digital landscape.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full group hover:shadow-xl transition-all duration-300 border-0 bg-gray-50 dark:bg-gray-800">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-4 mb-4">
                      <div
                        className={`p-3 rounded-lg bg-gradient-to-r ${service.color}`}
                      >
                        <service.icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">
                          {service.title}
                        </CardTitle>
                        <p className="text-sm text-blue-600 dark:text-blue-400 font-semibold">
                          {service.pricing}
                        </p>
                      </div>
                    </div>
                    <CardDescription className="text-base">
                      {service.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <ul className="space-y-3 mb-6">
                      {service.features.map((feature, featureIndex) => (
                        <li
                          key={featureIndex}
                          className="flex items-center text-sm text-gray-600 dark:text-gray-300"
                        >
                          <Check className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>

                    <Button variant="outline" className="w-full group">
                      Get Started
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Our Process
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              A proven methodology that ensures successful project delivery
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xl font-bold mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Let's discuss your project and find the perfect solution for your
              business needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg">
                Schedule Consultation
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                View Portfolio
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
