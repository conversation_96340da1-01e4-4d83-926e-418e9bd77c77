{"name": "basic", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.0", "@types/nodemailer": "^6.4.17", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^22.15.30", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5.8.3"}}